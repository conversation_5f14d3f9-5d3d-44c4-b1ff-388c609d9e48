"""
Specialized Temporal Neural Networks for Trading
Advanced time-aware neural architectures for temporal pattern recognition
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timezone
import math

logger = logging.getLogger(__name__)

class TemporalConvolutionalNetwork(nn.Module):
    """
    Temporal Convolutional Network (TCN) for time series pattern recognition
    Uses dilated causal convolutions for long-range temporal dependencies
    """
    
    def __init__(self, input_channels: int, output_size: int, 
                 num_channels: List[int], kernel_size: int = 3, dropout: float = 0.1):
        super().__init__()
        
        layers = []
        num_levels = len(num_channels)
        
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = input_channels if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            
            layers.append(
                TemporalBlock(
                    in_channels, out_channels, kernel_size,
                    stride=1, dilation=dilation_size, 
                    padding=(kernel_size-1) * dilation_size,
                    dropout=dropout
                )
            )
        
        self.network = nn.Sequential(*layers)
        self.linear = nn.Linear(num_channels[-1], output_size)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """Forward pass through TCN"""
        # x shape: (batch_size, input_channels, sequence_length)
        y = self.network(x)
        # Take last timestep and apply linear layer
        output = self.linear(self.dropout(y[:, :, -1]))
        return output


class TemporalBlock(nn.Module):
    """Individual temporal block for TCN with residual connections"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int,
                 stride: int, dilation: int, padding: int, dropout: float = 0.1):
        super().__init__()
        
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp1 = Chomp1d(padding)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size,
                              stride=stride, padding=padding, dilation=dilation)
        self.chomp2 = Chomp1d(padding)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        self.net = nn.Sequential(
            self.conv1, self.chomp1, self.relu1, self.dropout1,
            self.conv2, self.chomp2, self.relu2, self.dropout2
        )
        
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        self.relu = nn.ReLU()
        
    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class Chomp1d(nn.Module):
    """Chomp layer to ensure causal convolutions"""
    
    def __init__(self, chomp_size: int):
        super().__init__()
        self.chomp_size = chomp_size
        
    def forward(self, x):
        return x[:, :, :-self.chomp_size].contiguous()


class TemporalAttentionNetwork(nn.Module):
    """
    Temporal attention network for importance weighting across time steps
    Uses multi-head attention to focus on relevant temporal patterns
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, num_heads: int, num_layers: int):
        super().__init__()
        
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        self.positional_encoding = TemporalPositionalEncoding(hidden_dim)
        
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=0.1,
            batch_first=True
        )
        
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        self.temporal_attention = nn.MultiheadAttention(hidden_dim, num_heads, batch_first=True)
        self.output_projection = nn.Linear(hidden_dim, 1)
        self.layer_norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x, temporal_mask=None):
        """Forward pass with temporal attention"""
        # Project input to hidden dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        x = self.positional_encoding(x)
        
        # Transformer encoding
        encoded = self.transformer(x)
        
        # Temporal attention
        attended, attention_weights = self.temporal_attention(
            encoded, encoded, encoded, attn_mask=temporal_mask
        )
        
        # Layer normalization and output projection
        output = self.layer_norm(attended)
        output = self.output_projection(output)
        
        return output, attention_weights


class TimeSeriesTransformer(nn.Module):
    """
    Specialized transformer for time series analysis with temporal embeddings
    Incorporates time-aware positional encoding and temporal attention
    """
    
    def __init__(self, d_model: int, nhead: int, num_layers: int, 
                 sequence_length: int, output_dim: int = 1):
        super().__init__()
        
        self.d_model = d_model
        self.sequence_length = sequence_length
        
        # Input projection
        self.input_projection = nn.Linear(1, d_model)  # Assuming single feature input
        
        # Temporal positional encoding
        self.positional_encoding = TemporalPositionalEncoding(d_model, sequence_length)
        
        # Transformer layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=d_model * 4,
            dropout=0.1,
            batch_first=True
        )
        
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # Output layers
        self.output_norm = nn.LayerNorm(d_model)
        self.output_linear = nn.Linear(d_model, output_dim)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x):
        """Forward pass through time series transformer"""
        # Project input to model dimension
        x = self.input_projection(x)
        
        # Add positional encoding
        x = self.positional_encoding(x)
        
        # Transformer encoding
        encoded = self.transformer(x)
        
        # Output processing (use last timestep)
        output = self.output_norm(encoded[:, -1, :])  # Take last timestep
        output = self.dropout(output)
        output = self.output_linear(output)
        
        return output


class TemporalPositionalEncoding(nn.Module):
    """
    Positional encoding specialized for temporal data
    Incorporates both position and time-based encoding
    """
    
    def __init__(self, d_model: int, max_length: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_length, d_model)
        position = torch.arange(0, max_length, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe.unsqueeze(0))
        
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class TemporalLSTM(nn.Module):
    """
    Enhanced LSTM with temporal awareness and attention mechanisms
    """
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 2,
                 dropout: float = 0.1, bidirectional: bool = False):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bidirectional = bidirectional
        
        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=bidirectional,
            batch_first=True
        )
        
        # Attention mechanism
        lstm_output_size = hidden_size * 2 if bidirectional else hidden_size
        self.attention = nn.MultiheadAttention(
            embed_dim=lstm_output_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Output layers
        self.layer_norm = nn.LayerNorm(lstm_output_size)
        self.output_linear = nn.Linear(lstm_output_size, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """Forward pass through temporal LSTM"""
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # Apply attention
        attended, attention_weights = self.attention(lstm_out, lstm_out, lstm_out)
        
        # Layer normalization and output
        output = self.layer_norm(attended)
        output = self.dropout(output)
        
        # Use last timestep for prediction
        output = self.output_linear(output[:, -1, :])
        
        return output, attention_weights


class TemporalGRU(nn.Module):
    """
    Enhanced GRU with temporal pattern recognition capabilities
    """
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 2,
                 dropout: float = 0.1):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # GRU layers
        self.gru = nn.GRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # Temporal attention
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # Output layers
        self.layer_norm = nn.LayerNorm(hidden_size)
        self.output_linear = nn.Linear(hidden_size, 1)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """Forward pass through temporal GRU"""
        # GRU forward pass
        gru_out, hidden = self.gru(x)
        
        # Apply attention
        attended, attention_weights = self.attention(gru_out, gru_out, gru_out)
        
        # Layer normalization and output
        output = self.layer_norm(attended)
        output = self.dropout(output)
        
        # Use last timestep for prediction
        output = self.output_linear(output[:, -1, :])
        
        return output, attention_weights


class TemporalEnsemble(nn.Module):
    """
    Ensemble of temporal neural networks for robust predictions
    """
    
    def __init__(self, input_size: int, hidden_size: int = 128):
        super().__init__()
        
        # Different temporal models
        self.tcn = TemporalConvolutionalNetwork(
            input_channels=input_size,
            output_size=1,
            num_channels=[64, 128, 256],
            kernel_size=3,
            dropout=0.1
        )
        
        self.temporal_lstm = TemporalLSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=2,
            dropout=0.1
        )
        
        self.temporal_gru = TemporalGRU(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=2,
            dropout=0.1
        )
        
        # Ensemble combination
        self.ensemble_weights = nn.Parameter(torch.ones(3) / 3)
        self.final_linear = nn.Linear(3, 1)
        
    def forward(self, x):
        """Forward pass through temporal ensemble"""
        # TCN expects (batch, channels, sequence)
        tcn_input = x.transpose(1, 2)
        tcn_out = self.tcn(tcn_input)
        
        # LSTM and GRU expect (batch, sequence, features)
        lstm_out, _ = self.temporal_lstm(x)
        gru_out, _ = self.temporal_gru(x)
        
        # Combine outputs
        outputs = torch.stack([tcn_out.squeeze(), lstm_out.squeeze(), gru_out.squeeze()], dim=1)
        
        # Weighted ensemble
        weights = F.softmax(self.ensemble_weights, dim=0)
        weighted_output = torch.sum(outputs * weights.unsqueeze(0), dim=1, keepdim=True)
        
        # Final prediction
        final_output = self.final_linear(outputs)
        
        return final_output, weighted_output
