"""
Multi-Scale Time Analysis Engine
Comprehensive temporal analysis across multiple time scales for trading optimization
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import math
import time
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

class TimeScale(Enum):
    """Time scales for multi-scale analysis"""
    MICROSECOND = "microsecond"
    MILLISECOND = "millisecond"
    SECOND = "second"
    MINUTE = "minute"
    HOUR = "hour"
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"

@dataclass
class TimeScaleAnalysis:
    """Analysis results for a specific time scale"""
    scale: TimeScale
    timestamp: datetime
    
    # Pattern metrics
    volatility: float
    momentum: float
    trend_strength: float
    cyclical_strength: float
    
    # Market session metrics
    session_factor: float
    liquidity_factor: float
    volume_factor: float
    
    # Predictive metrics
    next_movement_probability: float
    optimal_entry_timing: float  # 0.0 to 1.0
    risk_factor: float
    
    # Confidence and quality
    confidence: float
    data_quality: float
    
    # Additional context
    market_conditions: Dict[str, Any] = field(default_factory=dict)

@dataclass
class MultiScaleTimeContext:
    """Complete multi-scale temporal context"""
    timestamp: datetime
    scale_analyses: Dict[TimeScale, TimeScaleAnalysis]
    
    # Cross-scale metrics
    scale_alignment: float  # How well scales agree
    temporal_momentum: float  # Overall momentum across scales
    optimal_time_horizon: TimeScale  # Best scale for current conditions
    
    # Trading recommendations
    recommended_action: str  # 'buy', 'sell', 'hold', 'wait'
    optimal_execution_window: Tuple[datetime, datetime]
    confidence_score: float
    
    # Risk assessment
    temporal_risk: float
    execution_urgency: float

class MultiScaleTimeAnalyzer:
    """
    Advanced multi-scale time analysis engine
    Analyzes market patterns across multiple temporal scales
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        
        # Scale-specific analyzers
        self.scale_analyzers = {
            scale: ScaleSpecificAnalyzer(scale) for scale in TimeScale
        }
        
        # Historical analysis cache
        self.analysis_history = defaultdict(lambda: deque(maxlen=1000))
        self.pattern_cache = {}
        
        # Performance tracking
        self.analysis_performance = {
            'accuracy': deque(maxlen=100),
            'execution_time': deque(maxlen=100),
            'prediction_quality': deque(maxlen=100)
        }
        
        # Real-time state
        self.current_context: Optional[MultiScaleTimeContext] = None
        self.last_analysis: Optional[datetime] = None
        
        logger.info("🕐 [MULTI-SCALE] Multi-scale time analyzer initialized")
    
    async def analyze_temporal_context(self, market_data: Dict[str, Any], 
                                     symbol: str) -> MultiScaleTimeContext:
        """Perform comprehensive multi-scale temporal analysis"""
        try:
            start_time = time.time()
            current_time = datetime.now(timezone.utc)
            
            # Analyze each time scale
            scale_analyses = {}
            analysis_tasks = []
            
            for scale in TimeScale:
                task = self._analyze_time_scale(scale, market_data, symbol, current_time)
                analysis_tasks.append((scale, task))
            
            # Execute analyses concurrently
            for scale, task in analysis_tasks:
                try:
                    analysis = await task
                    scale_analyses[scale] = analysis
                except Exception as e:
                    logger.error(f"❌ [MULTI-SCALE] Analysis failed for {scale.value}: {e}")
                    # Create fallback analysis
                    scale_analyses[scale] = self._create_fallback_analysis(scale, current_time)
            
            # Cross-scale analysis
            cross_scale_metrics = await self._analyze_cross_scale_patterns(scale_analyses)
            
            # Generate trading recommendations
            recommendations = await self._generate_temporal_recommendations(
                scale_analyses, cross_scale_metrics, market_data
            )
            
            # Create comprehensive context
            context = MultiScaleTimeContext(
                timestamp=current_time,
                scale_analyses=scale_analyses,
                **cross_scale_metrics,
                **recommendations
            )
            
            # Update performance tracking
            execution_time = time.time() - start_time
            self.analysis_performance['execution_time'].append(execution_time)
            
            # Cache results
            self.current_context = context
            self.last_analysis = current_time
            self.analysis_history[symbol].append(context)
            
            logger.info(f"✅ [MULTI-SCALE] Analysis completed in {execution_time:.3f}s")
            return context
            
        except Exception as e:
            logger.error(f"❌ [MULTI-SCALE] Temporal analysis failed: {e}")
            return self._create_fallback_context(current_time)
    
    async def _analyze_time_scale(self, scale: TimeScale, market_data: Dict[str, Any],
                                symbol: str, timestamp: datetime) -> TimeScaleAnalysis:
        """Analyze specific time scale"""
        try:
            analyzer = self.scale_analyzers[scale]
            
            # Extract scale-specific data
            scale_data = await self._extract_scale_data(market_data, scale)
            
            # Perform scale-specific analysis
            analysis = await analyzer.analyze(scale_data, symbol, timestamp)
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ [SCALE-ANALYSIS] Failed for {scale.value}: {e}")
            return self._create_fallback_analysis(scale, timestamp)
    
    async def _extract_scale_data(self, market_data: Dict[str, Any], 
                                scale: TimeScale) -> Dict[str, Any]:
        """Extract data relevant to specific time scale"""
        scale_data = {}
        
        try:
            if scale == TimeScale.MICROSECOND:
                # Order book dynamics, tick data
                scale_data = {
                    'bid_ask_spread': market_data.get('spread', 0.0),
                    'order_book_depth': market_data.get('depth', {}),
                    'tick_frequency': market_data.get('tick_frequency', 0.0)
                }
            
            elif scale == TimeScale.MILLISECOND:
                # High-frequency price movements
                scale_data = {
                    'price_changes': market_data.get('price_changes', []),
                    'volume_spikes': market_data.get('volume_spikes', []),
                    'latency_metrics': market_data.get('latency', {})
                }
            
            elif scale == TimeScale.SECOND:
                # Tick-by-tick analysis
                scale_data = {
                    'price_ticks': market_data.get('ticks', []),
                    'volume_profile': market_data.get('volume_profile', {}),
                    'momentum_indicators': market_data.get('momentum', {})
                }
            
            elif scale == TimeScale.MINUTE:
                # Short-term patterns
                scale_data = {
                    'minute_bars': market_data.get('minute_data', []),
                    'technical_indicators': market_data.get('indicators', {}),
                    'support_resistance': market_data.get('levels', {})
                }
            
            elif scale == TimeScale.HOUR:
                # Intraday cycles
                scale_data = {
                    'hourly_patterns': market_data.get('hourly_data', []),
                    'session_analysis': market_data.get('session', {}),
                    'volatility_patterns': market_data.get('volatility', {})
                }
            
            elif scale == TimeScale.DAY:
                # Daily patterns
                scale_data = {
                    'daily_data': market_data.get('daily_data', []),
                    'trend_analysis': market_data.get('trends', {}),
                    'seasonal_factors': market_data.get('seasonal', {})
                }
            
            elif scale == TimeScale.WEEK:
                # Weekly cycles
                scale_data = {
                    'weekly_patterns': market_data.get('weekly_data', []),
                    'cycle_analysis': market_data.get('cycles', {}),
                    'correlation_data': market_data.get('correlations', {})
                }
            
            elif scale == TimeScale.MONTH:
                # Monthly trends
                scale_data = {
                    'monthly_data': market_data.get('monthly_data', []),
                    'macro_trends': market_data.get('macro', {}),
                    'fundamental_data': market_data.get('fundamentals', {})
                }
            
            return scale_data
            
        except Exception as e:
            logger.error(f"❌ [DATA-EXTRACTION] Failed for {scale.value}: {e}")
            return {}
    
    async def _analyze_cross_scale_patterns(self, scale_analyses: Dict[TimeScale, TimeScaleAnalysis]) -> Dict[str, Any]:
        """Analyze patterns across multiple time scales"""
        try:
            # Calculate scale alignment
            scale_alignment = await self._calculate_scale_alignment(scale_analyses)
            
            # Calculate temporal momentum
            temporal_momentum = await self._calculate_temporal_momentum(scale_analyses)
            
            # Determine optimal time horizon
            optimal_time_horizon = await self._determine_optimal_horizon(scale_analyses)
            
            return {
                'scale_alignment': scale_alignment,
                'temporal_momentum': temporal_momentum,
                'optimal_time_horizon': optimal_time_horizon
            }
            
        except Exception as e:
            logger.error(f"❌ [CROSS-SCALE] Analysis failed: {e}")
            return {
                'scale_alignment': 0.5,
                'temporal_momentum': 0.0,
                'optimal_time_horizon': TimeScale.MINUTE
            }
    
    async def _calculate_scale_alignment(self, scale_analyses: Dict[TimeScale, TimeScaleAnalysis]) -> float:
        """Calculate how well different time scales align"""
        try:
            if len(scale_analyses) < 2:
                return 0.5
            
            # Extract trend directions from each scale
            trends = []
            for analysis in scale_analyses.values():
                if analysis.trend_strength > 0.5:
                    trends.append(1.0)  # Uptrend
                elif analysis.trend_strength < -0.5:
                    trends.append(-1.0)  # Downtrend
                else:
                    trends.append(0.0)  # Sideways
            
            if not trends:
                return 0.5
            
            # Calculate alignment as variance of trends
            trend_variance = np.var(trends)
            alignment = max(0.0, 1.0 - trend_variance)
            
            return alignment
            
        except Exception as e:
            logger.error(f"❌ [SCALE-ALIGNMENT] Calculation failed: {e}")
            return 0.5
    
    async def _calculate_temporal_momentum(self, scale_analyses: Dict[TimeScale, TimeScaleAnalysis]) -> float:
        """Calculate overall momentum across time scales"""
        try:
            if not scale_analyses:
                return 0.0
            
            # Weight momentum by scale importance
            scale_weights = {
                TimeScale.MICROSECOND: 0.05,
                TimeScale.MILLISECOND: 0.05,
                TimeScale.SECOND: 0.10,
                TimeScale.MINUTE: 0.20,
                TimeScale.HOUR: 0.25,
                TimeScale.DAY: 0.20,
                TimeScale.WEEK: 0.10,
                TimeScale.MONTH: 0.05
            }
            
            weighted_momentum = 0.0
            total_weight = 0.0
            
            for scale, analysis in scale_analyses.items():
                weight = scale_weights.get(scale, 0.1)
                weighted_momentum += analysis.momentum * weight
                total_weight += weight
            
            if total_weight > 0:
                return weighted_momentum / total_weight
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"❌ [TEMPORAL-MOMENTUM] Calculation failed: {e}")
            return 0.0
    
    async def _determine_optimal_horizon(self, scale_analyses: Dict[TimeScale, TimeScaleAnalysis]) -> TimeScale:
        """Determine the optimal time horizon for current conditions"""
        try:
            if not scale_analyses:
                return TimeScale.MINUTE
            
            # Score each scale based on confidence and signal strength
            scale_scores = {}
            
            for scale, analysis in scale_analyses.items():
                # Combine confidence, signal strength, and data quality
                signal_strength = abs(analysis.momentum) + abs(analysis.trend_strength)
                score = (analysis.confidence * 0.4 + 
                        signal_strength * 0.4 + 
                        analysis.data_quality * 0.2)
                scale_scores[scale] = score
            
            # Return scale with highest score
            optimal_scale = max(scale_scores, key=scale_scores.get)
            return optimal_scale
            
        except Exception as e:
            logger.error(f"❌ [OPTIMAL-HORIZON] Determination failed: {e}")
            return TimeScale.MINUTE
    
    async def _generate_temporal_recommendations(self, scale_analyses: Dict[TimeScale, TimeScaleAnalysis],
                                               cross_scale_metrics: Dict[str, Any],
                                               market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading recommendations based on temporal analysis"""
        try:
            # Determine recommended action
            temporal_momentum = cross_scale_metrics.get('temporal_momentum', 0.0)
            scale_alignment = cross_scale_metrics.get('scale_alignment', 0.5)
            
            if temporal_momentum > 0.3 and scale_alignment > 0.7:
                recommended_action = 'buy'
                confidence_score = min(0.9, scale_alignment + temporal_momentum * 0.5)
            elif temporal_momentum < -0.3 and scale_alignment > 0.7:
                recommended_action = 'sell'
                confidence_score = min(0.9, scale_alignment + abs(temporal_momentum) * 0.5)
            elif scale_alignment < 0.3:
                recommended_action = 'wait'
                confidence_score = 0.3
            else:
                recommended_action = 'hold'
                confidence_score = 0.5
            
            # Calculate optimal execution window
            current_time = datetime.now(timezone.utc)
            optimal_horizon = cross_scale_metrics.get('optimal_time_horizon', TimeScale.MINUTE)
            
            if optimal_horizon == TimeScale.SECOND:
                window_duration = timedelta(seconds=30)
            elif optimal_horizon == TimeScale.MINUTE:
                window_duration = timedelta(minutes=5)
            elif optimal_horizon == TimeScale.HOUR:
                window_duration = timedelta(minutes=30)
            else:
                window_duration = timedelta(minutes=15)
            
            optimal_execution_window = (current_time, current_time + window_duration)
            
            # Calculate temporal risk
            volatility_sum = sum(analysis.volatility for analysis in scale_analyses.values())
            avg_volatility = volatility_sum / len(scale_analyses) if scale_analyses else 0.5
            temporal_risk = min(1.0, avg_volatility * (1.0 - scale_alignment))
            
            # Calculate execution urgency
            execution_urgency = 1.0 - scale_alignment  # Higher urgency when scales don't align
            
            return {
                'recommended_action': recommended_action,
                'optimal_execution_window': optimal_execution_window,
                'confidence_score': confidence_score,
                'temporal_risk': temporal_risk,
                'execution_urgency': execution_urgency
            }
            
        except Exception as e:
            logger.error(f"❌ [RECOMMENDATIONS] Generation failed: {e}")
            return {
                'recommended_action': 'hold',
                'optimal_execution_window': (datetime.now(timezone.utc), 
                                           datetime.now(timezone.utc) + timedelta(minutes=5)),
                'confidence_score': 0.5,
                'temporal_risk': 0.5,
                'execution_urgency': 0.5
            }
    
    def _create_fallback_analysis(self, scale: TimeScale, timestamp: datetime) -> TimeScaleAnalysis:
        """Create fallback analysis when normal analysis fails"""
        return TimeScaleAnalysis(
            scale=scale,
            timestamp=timestamp,
            volatility=0.5,
            momentum=0.0,
            trend_strength=0.0,
            cyclical_strength=0.0,
            session_factor=0.5,
            liquidity_factor=0.5,
            volume_factor=0.5,
            next_movement_probability=0.5,
            optimal_entry_timing=0.5,
            risk_factor=0.5,
            confidence=0.3,
            data_quality=0.3
        )
    
    def _create_fallback_context(self, timestamp: datetime) -> MultiScaleTimeContext:
        """Create fallback context when analysis fails"""
        scale_analyses = {
            scale: self._create_fallback_analysis(scale, timestamp) 
            for scale in TimeScale
        }
        
        return MultiScaleTimeContext(
            timestamp=timestamp,
            scale_analyses=scale_analyses,
            scale_alignment=0.5,
            temporal_momentum=0.0,
            optimal_time_horizon=TimeScale.MINUTE,
            recommended_action='hold',
            optimal_execution_window=(timestamp, timestamp + timedelta(minutes=5)),
            confidence_score=0.3,
            temporal_risk=0.5,
            execution_urgency=0.5
        )


class ScaleSpecificAnalyzer:
    """Analyzer for specific time scales"""

    def __init__(self, scale: TimeScale):
        self.scale = scale
        self.analysis_cache = {}
        self.pattern_history = deque(maxlen=100)

    async def analyze(self, scale_data: Dict[str, Any], symbol: str,
                     timestamp: datetime) -> TimeScaleAnalysis:
        """Perform scale-specific analysis"""
        try:
            # Scale-specific analysis methods
            if self.scale == TimeScale.MICROSECOND:
                return await self._analyze_microsecond(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.MILLISECOND:
                return await self._analyze_millisecond(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.SECOND:
                return await self._analyze_second(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.MINUTE:
                return await self._analyze_minute(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.HOUR:
                return await self._analyze_hour(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.DAY:
                return await self._analyze_day(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.WEEK:
                return await self._analyze_week(scale_data, symbol, timestamp)
            elif self.scale == TimeScale.MONTH:
                return await self._analyze_month(scale_data, symbol, timestamp)
            else:
                return self._create_default_analysis(timestamp)

        except Exception as e:
            logger.error(f"❌ [SCALE-ANALYZER] {self.scale.value} analysis failed: {e}")
            return self._create_default_analysis(timestamp)

    async def _analyze_microsecond(self, data: Dict[str, Any], symbol: str,
                                 timestamp: datetime) -> TimeScaleAnalysis:
        """Analyze microsecond-level patterns"""
        try:
            # Order book analysis
            spread = data.get('bid_ask_spread', 0.0)
            depth = data.get('order_book_depth', {})
            tick_freq = data.get('tick_frequency', 0.0)

            # Calculate metrics
            volatility = min(1.0, spread * 1000)  # Normalize spread
            momentum = 0.0  # Microsecond momentum is noise
            trend_strength = 0.0
            cyclical_strength = 0.0

            # Market microstructure factors
            session_factor = 0.8 if tick_freq > 10 else 0.4
            liquidity_factor = 1.0 - min(1.0, spread * 100)
            volume_factor = min(1.0, len(depth) / 10.0) if depth else 0.5

            # Execution timing
            optimal_entry_timing = 1.0 - spread  # Tighter spread = better timing
            risk_factor = spread * 2  # Higher spread = higher risk

            return TimeScaleAnalysis(
                scale=self.scale,
                timestamp=timestamp,
                volatility=volatility,
                momentum=momentum,
                trend_strength=trend_strength,
                cyclical_strength=cyclical_strength,
                session_factor=session_factor,
                liquidity_factor=liquidity_factor,
                volume_factor=volume_factor,
                next_movement_probability=0.5,
                optimal_entry_timing=optimal_entry_timing,
                risk_factor=risk_factor,
                confidence=0.6,
                data_quality=0.8 if data else 0.3
            )

        except Exception as e:
            logger.error(f"❌ [MICROSECOND] Analysis failed: {e}")
            return self._create_default_analysis(timestamp)

    async def _analyze_millisecond(self, data: Dict[str, Any], symbol: str,
                                 timestamp: datetime) -> TimeScaleAnalysis:
        """Analyze millisecond-level patterns"""
        try:
            price_changes = data.get('price_changes', [])
            volume_spikes = data.get('volume_spikes', [])
            latency = data.get('latency_metrics', {})

            # Calculate high-frequency metrics
            if price_changes:
                volatility = np.std(price_changes)
                momentum = np.mean(price_changes[-10:]) if len(price_changes) >= 10 else 0.0
            else:
                volatility = 0.5
                momentum = 0.0

            # Volume spike analysis
            volume_factor = len(volume_spikes) / 100.0 if volume_spikes else 0.5

            # Latency impact
            avg_latency = latency.get('average', 50.0)  # ms
            session_factor = max(0.1, 1.0 - avg_latency / 1000.0)

            return TimeScaleAnalysis(
                scale=self.scale,
                timestamp=timestamp,
                volatility=min(1.0, volatility),
                momentum=max(-1.0, min(1.0, momentum)),
                trend_strength=0.0,
                cyclical_strength=0.0,
                session_factor=session_factor,
                liquidity_factor=volume_factor,
                volume_factor=volume_factor,
                next_movement_probability=0.5,
                optimal_entry_timing=session_factor,
                risk_factor=volatility,
                confidence=0.7,
                data_quality=0.8 if data else 0.3
            )

        except Exception as e:
            logger.error(f"❌ [MILLISECOND] Analysis failed: {e}")
            return self._create_default_analysis(timestamp)

    async def _analyze_second(self, data: Dict[str, Any], symbol: str,
                            timestamp: datetime) -> TimeScaleAnalysis:
        """Analyze second-level patterns"""
        try:
            ticks = data.get('price_ticks', [])
            volume_profile = data.get('volume_profile', {})
            momentum_indicators = data.get('momentum_indicators', {})

            if ticks and len(ticks) > 1:
                prices = [tick.get('price', 0) for tick in ticks]
                volumes = [tick.get('volume', 0) for tick in ticks]

                # Calculate metrics
                price_changes = np.diff(prices)
                volatility = np.std(price_changes) if len(price_changes) > 0 else 0.5
                momentum = np.mean(price_changes[-5:]) if len(price_changes) >= 5 else 0.0

                # Trend analysis
                if len(prices) >= 10:
                    trend_strength = np.corrcoef(range(len(prices)), prices)[0, 1]
                else:
                    trend_strength = 0.0

                # Volume analysis
                avg_volume = np.mean(volumes) if volumes else 0
                volume_factor = min(1.0, avg_volume / 1000.0)

            else:
                volatility = 0.5
                momentum = 0.0
                trend_strength = 0.0
                volume_factor = 0.5

            return TimeScaleAnalysis(
                scale=self.scale,
                timestamp=timestamp,
                volatility=min(1.0, volatility),
                momentum=max(-1.0, min(1.0, momentum)),
                trend_strength=max(-1.0, min(1.0, trend_strength)),
                cyclical_strength=0.0,
                session_factor=0.7,
                liquidity_factor=volume_factor,
                volume_factor=volume_factor,
                next_movement_probability=0.5 + momentum * 0.3,
                optimal_entry_timing=0.7,
                risk_factor=volatility,
                confidence=0.8,
                data_quality=0.9 if ticks else 0.3
            )

        except Exception as e:
            logger.error(f"❌ [SECOND] Analysis failed: {e}")
            return self._create_default_analysis(timestamp)

    def _create_default_analysis(self, timestamp: datetime) -> TimeScaleAnalysis:
        """Create default analysis when data is insufficient"""
        return TimeScaleAnalysis(
            scale=self.scale,
            timestamp=timestamp,
            volatility=0.5,
            momentum=0.0,
            trend_strength=0.0,
            cyclical_strength=0.0,
            session_factor=0.5,
            liquidity_factor=0.5,
            volume_factor=0.5,
            next_movement_probability=0.5,
            optimal_entry_timing=0.5,
            risk_factor=0.5,
            confidence=0.3,
            data_quality=0.3
        )
